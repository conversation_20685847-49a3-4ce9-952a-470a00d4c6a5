# MQTT 5.0 封装方案设计

## 1. 整体设计思路

### 1.1 设计目标

- **易用性**：提供简单易用的API，降低业务开发人员使用MQTT的门槛
- **灵活性**：支持MQTT 5.0的主要特性，满足各种复杂业务场景需求
- **可扩展性**：设计合理的扩展点，方便未来功能扩展
- **可靠性**：提供完善的错误处理、重连机制等保障稳定运行
- **可观测性**：集成完善的日志和监控机制，便于运维和问题排查

### 1.2 技术选型

- **MQTT客户端库**：Eclipse Paho MQTT 5.0 Client
- **开发框架**：Spring Boot自动配置方式，遵循yudao-framework的设计规范
- **序列化方案**：默认支持JSON格式，并提供扩展机制支持其他格式
- **持久化方案**：支持Redis存储消息，便于历史消息查询和故障恢复
- **监控方案**：集成Spring Boot Actuator，提供丰富的监控指标

## 2. 架构设计

### 2.1 整体架构

```
+----------------------------------+
|       应用层 (业务服务)            |
+----------------------------------+
                 |
                 | 依赖
                 v
+----------------------------------+
|  yudao-spring-boot-starter-mqtt  |
|                                  |
|  +------------------------------+|
|  |       自动配置组件            ||
|  +------------------------------+|
|  +------------------------------+|
|  |        客户端管理组件         ||
|  +------------------------------+|
|  +------------------------------+|
|  |        消息发布组件           ||
|  +------------------------------+|
|  +------------------------------+|
|  |        消息订阅组件           ||
|  +------------------------------+|
|  +------------------------------+|
|  |        连接管理组件           ||
|  +------------------------------+|
|  +------------------------------+|
|  |        注解支持组件           ||
|  +------------------------------+|
|  +------------------------------+|
|  |        持久化组件             ||
|  +------------------------------+|
|  +------------------------------+|
|  |        监控指标组件           ||
|  +------------------------------+|
+----------------------------------+
                 |
                 | 依赖
                 v
+----------------------------------+
|     Eclipse Paho MQTT 5.0 Client |
+----------------------------------+
                 |
                 | 通信
                 v
+----------------------------------+
|          MQTT Broker             |
+----------------------------------+
```

### 2.2 核心组件

1. **自动配置组件**：负责读取配置并初始化相关组件
2. **客户端管理组件**：负责管理MQTT客户端的生命周期
3. **消息发布组件**：提供消息发布的各种API
4. **消息订阅组件**：处理消息的订阅和回调
5. **连接管理组件**：处理连接状态和重连逻辑
6. **注解支持组件**：提供注解方式的消息订阅和处理
7. **持久化组件**：支持消息的持久化存储和查询
8. **监控指标组件**：收集和暴露MQTT相关的监控指标

### 2.3 数据流

1. **发布流程**：
   - 业务代码调用消息发布API
   - 消息发布组件处理消息格式转换
   - 客户端管理组件获取MQTT客户端
   - 通过Paho客户端发送消息到Broker
   - 监控指标组件记录发布统计信息
   - 持久化组件可选择性存储发布的消息

2. **订阅流程**：
   - 应用启动时，自动配置组件初始化订阅
   - 消息订阅组件注册回调处理器
   - 当消息到达时，调用对应的处理器
   - 处理器处理消息并传递给业务逻辑
   - 监控指标组件记录接收统计信息
   - 持久化组件可选择性存储接收的消息

## 3. 包结构设计

遵循yudao-framework的命名规范，包结构设计如下：

```
cn.iocoder.yudao.framework.mqtt
├── config                                // 配置类
│   ├── MqttProperties.java               // 属性配置类
│   ├── MqttAutoConfiguration.java        // 自动配置类
│   └── MqttConfiguration.java            // Bean配置类
├── core                                  // 核心组件 (优化后的结构)
│   ├── client                            // 客户端相关
│   │   ├── MqttClientManager.java        // 客户端管理器
│   │   ├── MqttClient.java               // MQTT客户端封装
│   │   ├── MqttClientBuilder.java        // 客户端构建器
│   │   ├── MqttConnectionListener.java   // 连接监听器
│   │   └── MqttConnectionOptions.java    // 连接选项封装
│   ├── publisher                         // 发布者相关
│   │   ├── MqttPublisher.java            // 消息发布接口
│   │   ├── DefaultMqttPublisher.java     // 默认发布实现
│   │   ├── MqttPublishOptions.java       // 发布选项封装
│   │   └── MqttPublishResult.java        // 发布结果封装
│   ├── subscriber                        // 订阅者相关
│   │   ├── MqttSubscriber.java           // 订阅管理器接口
│   │   ├── DefaultMqttSubscriber.java    // 默认订阅实现
│   │   ├── MqttSubscribeOptions.java     // 订阅选项封装
│   │   └── MqttSubscriptionManager.java  // 订阅关系管理
│   └── template                          // 模板类
│       └── MqttTemplate.java             // 消息操作模板
├── support                               // 支持类
│   ├── MqttConstants.java                // 常量定义
│   ├── MqttException.java                // 异常类
│   └── MessageConverter.java             // 消息转换器
├── message                               // 消息相关
│   ├── MqttMessageBuilder.java           // 消息构建器
│   ├── MqttMessageHandler.java           // 消息处理器接口
│   ├── AbstractMqttMessageHandler.java   // 抽象消息处理器
│   ├── MqttMessageFilter.java            // 消息过滤器接口
│   └── MqttMessageTransformer.java       // 消息转换器接口
├── annotation                            // 注解相关
│   ├── EnableMqtt.java                   // 启用MQTT注解
│   ├── MqttSubscribe.java                // 订阅注解
│   └── MqttSubscribeHandler.java         // 注解处理器
├── persistence                           // 持久化相关
│   ├── MqttMessageRepository.java        // 消息仓库接口
│   ├── RedisMqttMessageRepository.java   // Redis实现
│   └── MqttMessageDTO.java               // 消息数据传输对象
└── monitoring                            // 监控相关
    ├── MqttMetrics.java                  // 监控指标
    └── MqttHealthIndicator.java          // 健康检查
```

### 3.1 各模块职责

- **config**：负责配置的加载、解析和组件的自动装配
- **core**：提供核心功能实现，包括客户端管理、消息发布、订阅管理等
- **support**：提供辅助功能，如常量定义、异常处理、消息转换等
- **message**：专注于消息的构建、处理和回调
- **annotation**：提供基于注解的消息订阅和处理机制
- **persistence**：提供消息持久化存储和查询功能
- **monitoring**：提供监控指标收集和暴露功能

## 4. 配置设计

### 4.1 配置项设计

基于Spring Boot的配置机制，设计以下配置项：

```yaml
yudao:
  mqtt:
    # 是否启用MQTT功能
    enabled: true
    # 服务器配置
    server:
      # 服务器地址，支持多种协议如tcp://、ssl://等
      uri: tcp://broker.example.com:1883
      # 用户名
      username: yudao_client
      # 密码
      password: secret
    # 客户端配置
    client:
      # 客户端ID，默认自动生成，格式为：yudao-{应用名}-{随机UUID}
      id: 
      # 是否清除会话，默认false
      clean-session: false
      # 自动重连，默认true
      auto-reconnect: true
      # 会话过期时间(秒)，默认0表示会话不过期
      session-expiry-interval: 0
      # 连接超时时间(秒)，默认30
      connect-timeout: 30
      # 心跳间隔(秒)，默认60
      keep-alive-interval: 60
      # 最大飞行窗口，默认10
      max-inflight: 10
      # 遗嘱消息配置
      will:
        # 是否启用遗嘱消息，默认false
        enabled: false
        # 遗嘱消息主题
        topic: will/client/{client_id}
        # 遗嘱消息负载
        payload: Client disconnected.
        # 遗嘱消息质量QoS，默认1
        qos: 1
        # 遗嘱消息保留标志，默认false
        retained: false
    # 持久化配置
    persistence:
      # 是否启用消息持久化，默认false
      enabled: false
      # 消息过期天数，默认7天
      expire-days: 7
      # 每个主题最大消息数量，默认1000
      max-messages-per-topic: 1000
    # 监控配置
    metrics:
      # 是否启用监控指标，默认true
      enabled: true
```

### 4.2 自动配置类

```java
@Configuration
@ConditionalOnProperty(prefix = "yudao.mqtt", name = "enabled", havingValue = "true", matchIfMissing = true)
@EnableConfigurationProperties(MqttProperties.class)
public class MqttAutoConfiguration {
    // 配置客户端管理器
    @Bean
    @ConditionalOnMissingBean
    public MqttClientManager mqttClientManager(MqttProperties properties, 
                                             MqttConnectionListener connectionListener) {
        return new MqttClientManager(properties, connectionListener);
    }
    
    // 配置连接监听器
    @Bean
    @ConditionalOnMissingBean
    public MqttConnectionListener mqttConnectionListener(MqttProperties properties) {
        return new MqttConnectionListener(properties);
    }
    
    // 配置消息模板
    @Bean
    @ConditionalOnMissingBean
    public MqttTemplate mqttTemplate(MqttClientManager clientManager, 
                                    MqttProperties properties) {
        return new MqttTemplate(clientManager, properties);
    }
    
    // 配置订阅管理器
    @Bean
    @ConditionalOnMissingBean
    public MqttSubscriber mqttSubscriber(MqttTemplate mqttTemplate, 
                                       List<MqttMessageHandler> messageHandlers) {
        return new MqttSubscriber(mqttTemplate, messageHandlers);
    }
    
    // 配置注解处理器
    @Bean
    @ConditionalOnMissingBean
    public MqttSubscribeHandler mqttSubscribeHandler(MqttTemplate mqttTemplate) {
        return new MqttSubscribeHandler(mqttTemplate);
    }
    
    // 配置消息持久化
    @Bean
    @ConditionalOnProperty(prefix = "yudao.mqtt.persistence", name = "enabled", havingValue = "true")
    @ConditionalOnMissingBean
    public MqttMessageRepository mqttMessageRepository(RedisTemplate<String, Object> redisTemplate,
                                                     MqttProperties properties) {
        return new RedisMqttMessageRepository(redisTemplate, properties);
    }
    
    // 配置监控指标
    @Bean
    @ConditionalOnProperty(prefix = "yudao.mqtt.metrics", name = "enabled", havingValue = "true", matchIfMissing = true)
    @ConditionalOnMissingBean
    @ConditionalOnClass(MeterRegistry.class)
    public MqttMetrics mqttMetrics(MeterRegistry registry, MqttClientManager clientManager) {
        return new MqttMetrics(registry, clientManager);
    }
    
    // 配置健康检查
    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnClass(HealthIndicator.class)
    public MqttHealthIndicator mqttHealthIndicator(MqttClientManager clientManager) {
        return new MqttHealthIndicator(clientManager);
    }
}
```

### 4.3 属性配置类

```java
@ConfigurationProperties(prefix = "yudao.mqtt")
@Data
public class MqttProperties {
    /**
     * 是否启用MQTT功能
     */
    private boolean enabled = true;
    
    /**
     * 服务器配置
     */
    private Server server = new Server();
    
    /**
     * 客户端配置
     */
    private Client client = new Client();
    
    /**
     * 持久化配置
     */
    private Persistence persistence = new Persistence();
    
    /**
     * 监控配置
     */
    private Metrics metrics = new Metrics();
    
    @Data
    public static class Server {
        /**
         * 服务器地址
         */
        private String uri;
        
        /**
         * 用户名
         */
        private String username;
        
        /**
         * 密码
         */
        private String password;
    }
    
    @Data
    public static class Client {
        /**
         * 客户端ID
         */
        private String id;
        
        /**
         * 是否清除会话
         */
        private boolean cleanSession = false;
        
        /**
         * 自动重连
         */
        private boolean autoReconnect = true;
        
        /**
         * 会话过期时间(秒)
         */
        private long sessionExpiryInterval = 0;
        
        /**
         * 连接超时时间(秒)
         */
        private int connectTimeout = 30;
        
        /**
         * 心跳间隔(秒)
         */
        private int keepAliveInterval = 60;
        
        /**
         * 最大飞行窗口
         */
        private int maxInflight = 10;
        
        /**
         * 遗嘱消息配置
         */
        private Will will = new Will();
        
        @Data
        public static class Will {
            /**
             * 是否启用遗嘱消息
             */
            private boolean enabled = false;
            
            /**
             * 遗嘱消息主题
             */
            private String topic;
            
            /**
             * 遗嘱消息负载
             */
            private String payload;
            
            /**
             * 遗嘱消息质量QoS
             */
            private int qos = 1;
            
            /**
             * 遗嘱消息保留标志
             */
            private boolean retained = false;
        }
    }
    
    @Data
    public static class Persistence {
        /**
         * 是否启用消息持久化
         */
        private boolean enabled = false;
        
        /**
         * 消息过期天数
         */
        private int expireDays = 7;
        
        /**
         * 每个主题最大消息数量
         */
        private long maxMessagesPerTopic = 1000;
    }
    
    @Data
    public static class Metrics {
        /**
         * 是否启用监控指标
         */
        private boolean enabled = true;
    }
}
```

## 5. 核心组件设计

### 5.1 客户端管理器 (MqttClientManager)

客户端管理器负责MQTT客户端的创建、初始化、管理和销毁。

```java
@Slf4j
public class MqttClientManager implements DisposableBean {
    
    private final MqttAsyncClient mqttClient;
    private final MqttProperties properties;
    private final MqttConnectionListener connectionListener;
    
    /**
     * 创建MQTT客户端
     */
    public MqttClientManager(MqttProperties properties, MqttConnectionListener connectionListener) {
        this.properties = properties;
        this.connectionListener = connectionListener;
        this.mqttClient = createMqttClient();
        connect();
    }
    
    /**
     * 获取MQTT客户端
     */
    public MqttAsyncClient getMqttClient() {
        return mqttClient;
    }
    
    /**
     * 连接MQTT服务器
     */
    public synchronized void connect() {
        // 连接逻辑...
    }
    
    /**
     * 断开连接
     */
    public synchronized void disconnect() {
        // 断开连接逻辑...
    }
    
    /**
     * Spring容器销毁时调用
     */
    @Override
    public void destroy() {
        disconnect();
    }
    
    /**
     * 创建MQTT客户端
     */
    private MqttAsyncClient createMqttClient() {
        // 创建客户端逻辑...
    }
}
```

### 5.2 连接监听器 (MqttConnectionListener)

连接监听器负责处理MQTT客户端的连接状态变化。

```java
@Slf4j
public class MqttConnectionListener implements MqttCallback {
    
    private final MqttProperties properties;
    private final List<MqttMessageHandler> messageHandlers;
    
    /**
     * 连接丢失时调用
     */
    @Override
    public void connectionLost(Throwable cause) {
        log.error("MQTT连接已断开: {}", cause.getMessage(), cause);
        // 处理连接丢失...
    }
    
    /**
     * 消息到达时调用
     */
    @Override
    public void messageArrived(String topic, MqttMessage message) throws Exception {
        log.debug("接收到MQTT消息，主题: {}, QoS: {}", topic, message.getQos());
        // 分发消息到对应的处理器...
    }
    
    /**
     * 消息发送完成时调用
     */
    @Override
    public void deliveryComplete(IMqttDeliveryToken token) {
        log.debug("MQTT消息已发送");
    }
    
    /**
     * 连接完成时调用
     */
    public void onConnectComplete(boolean reconnect, String serverURI) {
        log.info("MQTT{}连接已建立，服务器: {}", reconnect ? "重新" : "", serverURI);
        // 处理连接完成...
    }
}
```

### 5.3 消息模板 (MqttTemplate)

消息模板提供发布消息的各种API，是业务代码与MQTT客户端交互的主要接口。

```java
@Slf4j
public class MqttTemplate {
    
    private final MqttClientManager clientManager;
    private final MqttProperties properties;
    
    /**
     * 发布消息（使用默认QoS=1）
     *
     * @param topic   主题
     * @param payload 消息内容
     * @return 消息ID
     */
    public CompletableFuture<Integer> publish(String topic, Object payload) {
        return publish(topic, payload, 1, false);
    }
    
    /**
     * 发布消息
     *
     * @param topic    主题
     * @param payload  消息内容
     * @param qos      服务质量等级（0、1、2）
     * @param retained 是否保留消息
     * @return 消息ID
     */
    public CompletableFuture<Integer> publish(String topic, Object payload, int qos, boolean retained) {
        // 消息发布逻辑...
    }
    
    /**
     * 发布消息（使用自定义消息属性）
     *
     * @param topic    主题
     * @param payload  消息内容
     * @param qos      服务质量等级
     * @param retained 是否保留消息
     * @param properties 消息属性
     * @return 消息ID
     */
    public CompletableFuture<Integer> publish(String topic, Object payload, int qos, boolean retained, MqttProperties properties) {
        // 带属性的消息发布逻辑...
    }
    
    /**
     * 订阅主题
     *
     * @param topic 主题
     * @param qos   服务质量等级
     * @return 订阅结果
     */
    public CompletableFuture<Boolean> subscribe(String topic, int qos) {
        // 订阅逻辑...
    }
    
    /**
     * 取消订阅
     *
     * @param topic 主题
     * @return 取消结果
     */
    public CompletableFuture<Boolean> unsubscribe(String topic) {
        // 取消订阅逻辑...
    }
}
```

### 5.4 订阅管理器 (MqttSubscriber)

订阅管理器负责管理消息处理器和主题的订阅关系。

```java
@Slf4j
public class MqttSubscriber implements SmartInitializingSingleton {
    
    private final MqttTemplate mqttTemplate;
    private final List<MqttMessageHandler> messageHandlers;
    
    /**
     * 在所有单例Bean初始化完成后执行订阅
     */
    @Override
    public void afterSingletonsInstantiated() {
        subscribeTopics();
    }
    
    /**
     * 订阅所有处理器关注的主题
     */
    private void subscribeTopics() {
        // 订阅逻辑...
    }
    
    /**
     * 根据主题获取消息处理器
     */
    public List<MqttMessageHandler> getHandlersForTopic(String topic) {
        // 匹配处理器逻辑...
    }
}
```

## 6. 消息处理设计

### 6.1 消息处理器接口

```java
public interface MqttMessageHandler {
    
    /**
     * 获取订阅的主题，支持通配符(#和+)
     */
    String getTopic();
    
    /**
     * 获取订阅的QoS
     */
    default int getQos() {
        return 1;
    }
    
    /**
     * 处理MQTT消息
     *
     * @param topic        消息主题
     * @param payload      消息负载
     * @param qos          QoS等级
     * @param retained     是否保留消息
     * @param properties   消息属性
     */
    void handleMessage(String topic, byte[] payload, int qos, boolean retained, MqttProperties properties);
}
```

### 6.2 抽象消息处理器

```java
@Slf4j
public abstract class AbstractMqttMessageHandler implements MqttMessageHandler {
    
    /**
     * 默认实现消息处理方法，提供通用的日志和异常处理
     */
    @Override
    public void handleMessage(String topic, byte[] payload, int qos, boolean retained, MqttProperties properties) {
        try {
            log.debug("处理MQTT消息，主题: {}, 负载大小: {} 字节", topic, payload.length);
            processMessage(topic, payload, qos, retained, properties);
        } catch (Exception e) {
            log.error("处理MQTT消息异常，主题: {}", topic, e);
        }
    }
    
    /**
     * 子类实现的具体消息处理逻辑
     */
    protected abstract void processMessage(String topic, byte[] payload, int qos, boolean retained, MqttProperties properties);
}
```

### 6.3 消息构建器

```java
public class MqttMessageBuilder {
    
    private final String topic;
    private byte[] payload;
    private int qos = 1;
    private boolean retained = false;
    private MqttProperties properties;
    
    private MqttMessageBuilder(String topic) {
        this.topic = topic;
    }
    
    /**
     * 创建消息构建器
     */
    public static MqttMessageBuilder topic(String topic) {
        return new MqttMessageBuilder(topic);
    }
    
    /**
     * 设置消息负载
     */
    public MqttMessageBuilder payload(byte[] payload) {
        this.payload = payload;
        return this;
    }
    
    /**
     * 设置消息负载（字符串）
     */
    public MqttMessageBuilder payload(String payload) {
        this.payload = payload.getBytes(StandardCharsets.UTF_8);
        return this;
    }
    
    /**
     * 设置消息负载（对象，将转换为JSON）
     */
    public MqttMessageBuilder payload(Object payload) {
        try {
            this.payload = JsonUtils.toJsonString(payload).getBytes(StandardCharsets.UTF_8);
        } catch (Exception e) {
            throw new MqttException("转换消息负载为JSON失败", e);
        }
        return this;
    }
    
    /**
     * 设置QoS
     */
    public MqttMessageBuilder qos(int qos) {
        this.qos = qos;
        return this;
    }
    
    /**
     * 设置是否保留消息
     */
    public MqttMessageBuilder retained(boolean retained) {
        this.retained = retained;
        return this;
    }
    
    /**
     * 设置消息属性
     */
    public MqttMessageBuilder properties(MqttProperties properties) {
        this.properties = properties;
        return this;
    }
    
    /**
     * 构建MQTT消息
     */
    public MqttMessage build() {
        // 构建消息逻辑...
    }
}
```

## 7. 注解支持设计

### 7.1 EnableMqtt 注解

```java
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Import(MqttConfiguration.class)
public @interface EnableMqtt {
}
```

### 7.2 MqttSubscribe 注解

```java
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface MqttSubscribe {
    /**
     * 订阅主题，支持MQTT通配符(#和+)
     */
    String[] topics();

    /**
     * 服务质量
     */
    int qos() default 1;
    
    /**
     * 共享订阅组名，用于MQTT 5.0共享订阅
     * 如果指定，则订阅格式为 $share/{shareGroup}/{topic}
     */
    String shareGroup() default "";
}
```

### 7.3 订阅注解处理器

```java
@Slf4j
public class MqttSubscribeHandler implements BeanPostProcessor, ApplicationContextAware {
    
    private final MqttTemplate mqttTemplate;
    private ApplicationContext applicationContext;
    
    /**
     * 处理带有@MqttSubscribe注解的方法
     */
    @Override
    public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
        Class<?> clazz = bean.getClass();
        Method[] methods = clazz.getDeclaredMethods();
        
        for (Method method : methods) {
            MqttSubscribe annotation = method.getAnnotation(MqttSubscribe.class);
            if (annotation != null) {
                processSubscribeAnnotation(bean, method, annotation);
            }
        }
        
        return bean;
    }
    
    /**
     * 处理订阅注解
     */
    private void processSubscribeAnnotation(Object bean, Method method, MqttSubscribe annotation) {
        String[] topics = annotation.topics();
        int qos = annotation.qos();
        String shareGroup = annotation.shareGroup();
        
        for (String topic : topics) {
            // 处理共享订阅
            if (!StringUtils.isEmpty(shareGroup)) {
                topic = "$share/" + shareGroup + "/" + topic;
            }
            
            log.info("注册MQTT订阅: {} (QoS={})", topic, qos);
            
            final String finalTopic = topic;
            mqttTemplate.subscribe(topic, qos, (receivedTopic, message) -> {
                try {
                    invokeHandler(bean, method, finalTopic, message);
                } catch (Exception e) {
                    log.error("处理MQTT消息异常，主题: {}", receivedTopic, e);
                }
            });
        }
    }
    
    /**
     * 调用处理方法
     */
    private void invokeHandler(Object bean, Method method, String topic, MqttMessage message) throws Exception {
        Class<?>[] paramTypes = method.getParameterTypes();
        
        if (paramTypes.length == 0) {
            // 无参方法
            method.invoke(bean);
        } else if (paramTypes.length == 1) {
            // 单参方法 - 只接收消息
            if (MqttMessage.class.isAssignableFrom(paramTypes[0])) {
                method.invoke(bean, message);
            } else {
                Object payload = convertPayload(message.getPayload(), paramTypes[0]);
                method.invoke(bean, payload);
            }
        } else if (paramTypes.length == 2) {
            // 双参方法 - 接收主题和消息
            if (String.class.equals(paramTypes[0]) && MqttMessage.class.isAssignableFrom(paramTypes[1])) {
                method.invoke(bean, topic, message);
            } else if (String.class.equals(paramTypes[0])) {
                Object payload = convertPayload(message.getPayload(), paramTypes[1]);
                method.invoke(bean, topic, payload);
            }
        }
    }
    
    /**
     * 转换消息负载
     */
    private Object convertPayload(byte[] payload, Class<?> targetType) {
        if (byte[].class.equals(targetType)) {
            return payload;
        } else if (String.class.equals(targetType)) {
            return new String(payload, StandardCharsets.UTF_8);
        } else {
            // 使用转换器进行转换
            return applicationContext.getBean(MqttMessageConverter.class).convertFromBytes(payload, targetType);
        }
    }
    
    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
}
```

## 8. 持久化设计

### 8.1 消息持久化接口

```java
/**
 * MQTT消息持久化接口
 */
public interface MqttMessageRepository {
    
    /**
     * 保存消息
     *
     * @param topic 主题
     * @param message 消息
     */
    void saveMessage(String topic, MqttMessage message);
    
    /**
     * 获取消息
     *
     * @param topic 主题
     * @param limit 限制数量
     * @return 消息列表
     */
    List<MqttMessageDTO> getMessages(String topic, int limit);
}
```

### 8.2 基于Redis的消息持久化实现

```java
/**
 * 基于Redis实现的MQTT消息持久化
 */
@Component
@ConditionalOnProperty(prefix = "yudao.mqtt", name = "persistence.enabled", havingValue = "true")
public class RedisMqttMessageRepository implements MqttMessageRepository {
    
    private final RedisTemplate<String, Object> redisTemplate;
    private final YudaoMqttProperties properties;
    
    private static final String KEY_PREFIX = "mqtt:message:";
    
    @Override
    public void saveMessage(String topic, MqttMessage message) {
        String key = KEY_PREFIX + topic;
        MqttMessageDTO dto = convertToDto(topic, message);
        
        // 使用Redis列表存储
        redisTemplate.opsForList().leftPush(key, dto);
        
        // 设置过期时间
        if (properties.getPersistence().getExpireDays() > 0) {
            redisTemplate.expire(key, properties.getPersistence().getExpireDays(), TimeUnit.DAYS);
        }
        
        // 限制列表长度
        long maxCount = properties.getPersistence().getMaxMessagesPerTopic();
        if (maxCount > 0) {
            redisTemplate.opsForList().trim(key, 0, maxCount - 1);
        }
    }
    
    @Override
    public List<MqttMessageDTO> getMessages(String topic, int limit) {
        String key = KEY_PREFIX + topic;
        return redisTemplate.opsForList().range(key, 0, limit - 1);
    }
    
    private MqttMessageDTO convertToDto(String topic, MqttMessage message) {
        MqttMessageDTO dto = new MqttMessageDTO();
        dto.setTopic(topic);
        dto.setPayload(message.getPayload());
        dto.setQos(message.getQos());
        dto.setRetained(message.isRetained());
        dto.setTimestamp(System.currentTimeMillis());
        
        // 如果是MQTT 5.0消息，还可以保存用户属性等
        if (message.getProperties() != null) {
            dto.setUserProperties(extractUserProperties(message.getProperties()));
        }
        
        return dto;
    }
    
    private Map<String, String> extractUserProperties(MqttProperties properties) {
        Map<String, String> result = new HashMap<>();
        if (properties.getUserProperties() != null) {
            for (UserProperty prop : properties.getUserProperties()) {
                result.put(prop.getKey(), prop.getValue());
            }
        }
        return result;
    }
}
```

## 9. 监控指标设计

### 9.1 MQTT监控指标

```java
/**
 * MQTT监控指标
 */
@Component
@ConditionalOnClass(MeterRegistry.class)
@ConditionalOnProperty(prefix = "yudao.mqtt", name = "metrics.enabled", havingValue = "true")
public class MqttMetrics {
    
    private final Counter messagesPublished;
    private final Counter messagesReceived;
    private final Gauge connectionGauge;
    private final Timer publishTimer;
    
    public MqttMetrics(MeterRegistry registry, MqttClientManager clientManager) {
        messagesPublished = Counter.builder("mqtt.messages.published")
                .description("已发布的MQTT消息数量")
                .register(registry);
        
        messagesReceived = Counter.builder("mqtt.messages.received")
                .description("已接收的MQTT消息数量")
                .register(registry);
        
        connectionGauge = Gauge.builder("mqtt.connection", clientManager, 
                cm -> cm.getMqttClient().isConnected() ? 1 : 0)
                .description("MQTT连接状态 (1=已连接, 0=已断开)")
                .register(registry);
        
        publishTimer = Timer.builder("mqtt.publish.time")
                .description("MQTT消息发布耗时")
                .register(registry);
    }
    
    // 记录已发布消息
    public void incrementPublished() {
        messagesPublished.increment();
    }
    
    // 记录已接收消息
    public void incrementReceived() {
        messagesReceived.increment();
    }
    
    // 记录消息发布时间
    public Timer.Sample startPublishTimer() {
        return Timer.start();
    }
    
    // 停止计时并记录
    public void stopPublishTimer(Timer.Sample sample) {
        sample.stop(publishTimer);
    }
}
```

### 9.2 MQTT健康检查

```java
/**
 * MQTT健康检查
 */
@Component
@ConditionalOnClass(HealthIndicator.class)
public class MqttHealthIndicator implements HealthIndicator {
    
    private final MqttClientManager clientManager;
    
    @Override
    public Health health() {
        boolean connected = false;
        try {
            connected = clientManager.getMqttClient().isConnected();
            if (connected) {
                return Health.up()
                        .withDetail("status", "connected")
                        .withDetail("serverUri", clientManager.getMqttClient().getServerURI())
                        .build();
            } else {
                return Health.down()
                        .withDetail("status", "disconnected")
                        .withDetail("serverUri", clientManager.getMqttClient().getServerURI())
                        .build();
            }
        } catch (Exception e) {
            return Health.down()
                    .withDetail("status", "error")
                    .withDetail("error", e.getMessage())
                    .build();
        }
    }
}
```

## 10. MQTT 5.0 特性支持

### 10.1 会话过期间隔（Session Expiry Interval）

```java
// 在连接选项中设置会话过期间隔
MqttConnectionOptions options = new MqttConnectionOptions();
options.setSessionExpiryInterval(properties.getClient().getSessionExpiryInterval());
```

### 10.2 消息过期间隔（Message Expiry Interval）

```java
// 在发布消息时设置消息过期间隔
MqttProperties mqttProperties = new MqttProperties();
mqttProperties.setMessageExpiryInterval(3600); // 消息有效期1小时
mqttTemplate.publish("topic/example", payload, 1, false, mqttProperties);
```

### 10.3 主题别名（Topic Alias）

```java
// 在发布消息时设置主题别名
MqttProperties mqttProperties = new MqttProperties();
mqttProperties.setTopicAlias(10);
mqttTemplate.publish("topic/example", payload, 1, false, mqttProperties);
```

### 10.4 用户属性（User Properties）

```java
// 在发布消息时添加用户属性
MqttProperties mqttProperties = new MqttProperties();
List<UserProperty> userProperties = new ArrayList<>();
userProperties.add(new UserProperty("deviceId", "device-123"));
userProperties.add(new UserProperty("timestamp", String.valueOf(System.currentTimeMillis())));
mqttProperties.setUserProperties(userProperties);
mqttTemplate.publish("topic/example", payload, 1, false, mqttProperties);

// 在消息处理器中获取用户属性
@Override
protected void processMessage(String topic, byte[] payload, int qos, boolean retained, MqttProperties properties) {
    List<UserProperty> userProps = properties.getUserProperties();
    for (UserProperty prop : userProps) {
        log.info("收到用户属性: {}={}", prop.getKey(), prop.getValue());
    }
}
```

### 10.5 共享订阅（Shared Subscriptions）

```java
// 使用共享订阅
mqttTemplate.subscribe("$share/group1/topic/example", 1);

// 在消息处理器中使用共享订阅
@Component
public class SharedTopicHandler extends AbstractMqttMessageHandler {
    
    @Override
    public String getTopic() {
        return "$share/group1/topic/example";
    }
    
    @Override
    protected void processMessage(String topic, byte[] payload, int qos, boolean retained, MqttProperties properties) {
        // 处理共享订阅消息...
    }
}
```

## 11. 使用示例

### 11.1 引入依赖

```xml
<dependency>
    <groupId>cn.iocoder.boot</groupId>
    <artifactId>yudao-spring-boot-starter-mqtt</artifactId>
    <version>${revision}</version>
</dependency>
```

### 11.2 配置MQTT

```yaml
# application.yml
yudao:
  mqtt:
    enabled: true
    server:
      uri: tcp://mqtt.example.com:1883
      username: yudao_client
      password: secret
    client:
      session-expiry-interval: 3600
      auto-reconnect: true
```

### 11.3 创建消息处理器

```java
@Component
public class DeviceDataHandler extends AbstractMqttMessageHandler {
    
    @Override
    public String getTopic() {
        return "device/+/data";
    }
    
    @Override
    protected void processMessage(String topic, byte[] payload, int qos, boolean retained, MqttProperties properties) {
        // 从主题中提取设备ID
        String deviceId = topic.split("/")[1];
        
        // 解析消息内容，这里假设是JSON格式
        String jsonContent = new String(payload, StandardCharsets.UTF_8);
        try {
            DeviceData data = JsonUtils.parseObject(jsonContent, DeviceData.class);
            log.info("收到设备[{}]数据：{}", deviceId, data);
            
            // 业务处理逻辑...
        } catch (Exception e) {
            log.error("解析设备数据失败", e);
        }
    }
}
```

### 11.4 发布消息

```java
@Service
public class DeviceCommandService {
    
    @Autowired
    private MqttTemplate mqttTemplate;
    
    /**
     * 向设备发送命令
     */
    public void sendCommand(String deviceId, DeviceCommand command) {
        String topic = "device/" + deviceId + "/command";
        
        // 使用消息构建器构建消息
        CompletableFuture<Integer> future = mqttTemplate.publish(
                MqttMessageBuilder.topic(topic)
                        .payload(command)
                        .qos(1)
                        .build()
        );
        
        // 异步处理发送结果
        future.thenAccept(messageId -> {
            log.info("命令已发送，消息ID: {}", messageId);
        }).exceptionally(ex -> {
            log.error("发送命令失败", ex);
            return null;
        });
    }
}
```

## 12. 实施计划

### 12.1 阶段一：基础结构实现

1. 创建starter项目结构
2. 实现配置类和自动配置
3. 实现客户端管理组件
4. 实现基本的连接监听器

### 12.2 阶段二：核心功能实现

1. 实现消息模板
2. 实现订阅管理器
3. 实现消息处理器接口及抽象类
4. 实现消息构建器

### 12.3 阶段三：MQTT 5.0特性支持

1. 会话过期间隔
2. 消息过期间隔
3. 主题别名
4. 用户属性
5. 共享订阅

### 12.4 阶段四：测试和优化

1. 单元测试
2. 集成测试
3. 性能测试
4. 文档编写

## 13. 总结

本MQTT 5.0封装方案遵循yudao-framework的设计风格，提供了一套完整的MQTT客户端解决方案。通过合理的架构设计和API封装，使业务开发人员能够轻松地使用MQTT进行消息收发，同时充分利用MQTT 5.0的新特性。方案的实现考虑了易用性、灵活性、可扩展性和可靠性，为业务系统提供了强大的MQTT消息通信能力。
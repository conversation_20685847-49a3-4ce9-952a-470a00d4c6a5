<!-- 产品选择对话框 -->
 <template>
   <Dialog title="选择待采购产品" v-model="dialogVisible" :appendToBody="true" :scroll="true" width="1080">
     <ContentWrap>
       <!-- 搜索工作栏 -->
       <el-form
         class="-mb-15px"
         :model="queryParams"
         ref="queryFormRef"
         :inline="true"
         label-width="68px"
       >
         <el-form-item label="产品名称" prop="name">
           <el-input
             v-model="queryParams.name"
             placeholder="请输入产品名称"
             clearable
             @keyup.enter="handleQuery"
             class="!w-240px"
           />
         </el-form-item>
         <el-form-item label="产品编码" prop="code">
           <el-input
             v-model="queryParams.code"
             placeholder="请输入产品编码"
             clearable
             @keyup.enter="handleQuery"
             class="!w-240px"
           />
         </el-form-item>
         <el-form-item>
           <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
           <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
         </el-form-item>
       </el-form>
     </ContentWrap>

     <ContentWrap>
       <el-table
         v-loading="loading"
         :data="list"
         :show-overflow-tooltip="true"
         :stripe="true"
         @selection-change="handleSelectionChange"
         ref="tableRef"
       >
         <el-table-column type="selection" width="55" />
         <el-table-column label="产品编码" align="center" prop="code" />
         <el-table-column label="产品名称" align="center" prop="name" />
         <el-table-column label="产品分类" align="center" prop="categoryName" />
         <el-table-column label="单位" align="center" prop="unitName" />
         <el-table-column
           label="采购价格"
           align="center"
           prop="purchasePrice"
           :formatter="erpPriceTableColumnFormatter"
         />
         <el-table-column label="采购数量" align="center" prop="purchaseCount" min-width="140" fixed="right">
           <template #default="{ row }">
             <el-input-number v-model="row.purchaseCount" :min="1" controls-position="right" class="!w-100%" />
           </template>
         </el-table-column>
       </el-table>
       <!-- 分页 -->
       <Pagination
         v-model:limit="queryParams.pageSize"
         v-model:page="queryParams.pageNo"
         :total="total"
         @pagination="getList"
       />
     </ContentWrap>
     <template #footer>
       <el-button :disabled="selectionList.length === 0" type="primary" @click="submitForm">
         确 定
       </el-button>
       <el-button @click="dialogVisible = false">取 消</el-button>
     </template>
   </Dialog>
 </template>
 <script lang="ts" setup>
 import { erpPriceTableColumnFormatter } from '@/utils'
 import { ProductApi} from '@/api/erp/product/product'

 defineOptions({ name: 'ProductSelectionDialog' })

 const message = useMessage() // 消息弹窗

 const list = ref<any[]>([]) // 列表的数据, 使用 any[] 来方便添加 purchaseCount
 const total = ref(0) // 列表的总页数
 const loading = ref(false) // 列表的加载中
 const dialogVisible = ref(false) // 弹窗的是否展示
 const queryParams = reactive({
   pageNo: 1,
   pageSize: 10,
   name: undefined,
   code: undefined
 })
 const queryFormRef = ref() // 搜索的表单
 const selectionList = ref<any[]>([]) // 选中的产品列表

 /** 打开弹窗 */
 const open = async () => {
   dialogVisible.value = true
   await nextTick() // 等待，避免 queryFormRef 为空
   await resetQuery()
 }
 defineExpose({ open }) // 提供 open 方法，用于打开弹窗

 /** 提交选择 */
 const emits = defineEmits<{
   (e: 'success', value: any[]): void
 }>()
 const submitForm = () => {
   if (selectionList.value.length === 0) {
     message.warning('请选择要采购的产品')
     return
   }
   // 校验采购数量
   if (selectionList.value.some((item) => !item.purchaseCount || item.purchaseCount <= 0)) {
     message.warning('请输入有效的采购数量（大于0）')
     return
   }
   emits('success', JSON.parse(JSON.stringify(selectionList.value))) // 深拷贝，避免后续操作影响
   dialogVisible.value = false
 }

 /** 加载列表 */
 const getList = async () => {
   loading.value = true
   try {
     const data = await ProductApi.getProductPage(queryParams)
     list.value = data.list.map((item) => ({
       ...item,
       purchaseCount: 1 // 默认采购数量为 1
     }))
     total.value = data.total
   } finally {
     loading.value = false
   }
 }

 const handleQuery = () => {
   queryParams.pageNo = 1
   getList()
 }

 const resetQuery = () => {
   queryFormRef.value?.resetFields()
   handleQuery()
 }

 const handleSelectionChange = (rows: any[]) => {
   selectionList.value = rows
 }
 </script>

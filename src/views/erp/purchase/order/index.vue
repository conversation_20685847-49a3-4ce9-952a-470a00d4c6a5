<template>
  <doc-alert title="【采购】采购订单、入库、退货" url="https://doc.iocoder.cn/erp/purchase/" />

  <!-- 新增：产品选择对话框 -->
  <ProductSelectionDialog ref="productSelectionDialogRef" @success="handleGenerateOrders" />

  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      v-loading="loading"
      :element-loading-text="loadingText"
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="订单单号" prop="no">
        <el-input
          v-model="queryParams.no"
          placeholder="请输入订单单号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="产品" prop="productId">
        <el-select
          v-model="queryParams.productId"
          clearable
          filterable
          placeholder="请选择产品"
          class="!w-240px"
        >
          <el-option
            v-for="item in productList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="订单时间" prop="orderTime">
        <el-date-picker
          v-model="queryParams.orderTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="供应商" prop="supplierId">
        <el-select
          v-model="queryParams.supplierId"
          clearable
          filterable
          placeholder="请选择供供应商"
          class="!w-240px"
        >
          <el-option
            v-for="item in supplierList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="创建人" prop="creator">
        <el-select
          v-model="queryParams.creator"
          clearable
          filterable
          placeholder="请选择创建人"
          class="!w-240px"
        >
          <el-option
            v-for="item in userList"
            :key="item.id"
            :label="item.nickname"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable class="!w-240px">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ERP_AUDIT_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="queryParams.remark"
          placeholder="请输入备注"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="入库数量" prop="inStatus">
        <el-select
          v-model="queryParams.inStatus"
          placeholder="请选择入库数量"
          clearable
          class="!w-240px"
        >
          <el-option label="未入库" value="0" />
          <el-option label="部分入库" value="1" />
          <el-option label="全部入库" value="2" />
        </el-select>
      </el-form-item>
      <el-form-item label="退货数量" prop="returnStatus">
        <el-select
          v-model="queryParams.returnStatus"
          placeholder="请选择退货数量"
          clearable
          class="!w-240px"
        >
          <el-option label="未退货" value="0" />
          <el-option label="部分退货" value="1" />
          <el-option label="全部退货" value="2" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          @click="openProductSelectionDialog"
          v-hasPermi="['erp:purchase-order:create']"
        >
          <Icon icon="ep:shopping-cart-full" class="mr-5px" /> 智能采购
        </el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['erp:purchase-order:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="warning"
          plain
          @click="openSmartPurchase"
          :loading="smartPurchaseLoading"
          v-hasPermi="['erp:purchase-order:create']"
        >
          <Icon icon="ep:magic-stick" class="mr-5px" /> 智能采购
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['erp:purchase-order:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
        <el-button
          type="danger"
          plain
          @click="handleDelete(selectionList.map((item) => item.id))"
          v-hasPermi="['erp:purchase-order:delete']"
          :disabled="selectionList.length === 0"
        >
          <Icon icon="ep:delete" class="mr-5px" /> 删除
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table
      v-loading="loading"
      :data="list"
      :stripe="true"
      :show-overflow-tooltip="true"
      @selection-change="handleSelectionChange"
    >
      <el-table-column width="30" label="选择" type="selection" />
      <el-table-column min-width="180" label="订单单号" align="center" prop="no" />
      <el-table-column label="产品信息" align="center" prop="productNames" min-width="200">
        <template #default="scope">
          <div v-if="scope.row.productNames">
            <div v-for="(product, index) in scope.row.productNames.split('，')" :key="index" class="text-left">
              {{ product }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="供应商" align="center" prop="supplierName" />
      <el-table-column
        label="订单时间"
        align="center"
        prop="orderTime"
        :formatter="dateFormatter2"
        width="120px"
      />
      <el-table-column label="创建人" align="center" prop="creatorName" />
      <el-table-column
        label="总数量"
        align="center"
        prop="totalCount"
        :formatter="erpCountTableColumnFormatter"
      />
      <el-table-column
        label="入库数量"
        align="center"
        prop="inCount"
        :formatter="erpCountTableColumnFormatter"
      />
      <el-table-column
        label="退货数量"
        align="center"
        prop="returnCount"
        :formatter="erpCountTableColumnFormatter"
      />
      <el-table-column
        label="金额合计"
        align="center"
        prop="totalProductPrice"
        :formatter="erpPriceTableColumnFormatter"
      />
      <el-table-column
        label="含税金额"
        align="center"
        prop="totalPrice"
        :formatter="erpPriceTableColumnFormatter"
      />
      <el-table-column
        label="支付订金"
        align="center"
        prop="depositPrice"
        :formatter="erpPriceTableColumnFormatter"
      />
      <el-table-column label="状态" align="center" fixed="right" width="90" prop="status">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.ERP_AUDIT_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" fixed="right" width="220">
        <template #default="scope">
          <el-button
            link
            @click="openForm('detail', scope.row.id)"
            v-hasPermi="['erp:purchase-order:query']"
          >
            详情
          </el-button>
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['erp:purchase-order:update']"
            :disabled="scope.row.status === 20"
          >
            编辑
          </el-button>
          <el-button
            link
            type="primary"
            @click="handleUpdateStatus(scope.row.id, 20)"
            v-hasPermi="['erp:purchase-order:update-status']"
            v-if="scope.row.status === 10"
          >
            审批
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleUpdateStatus(scope.row.id, 10)"
            v-hasPermi="['erp:purchase-order:update-status']"
            v-else
          >
            反审批
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete([scope.row.id])"
            v-hasPermi="['erp:purchase-order:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <PurchaseOrderForm ref="formRef" @success="getList" />

  <!-- 智能采购产品选择对话框 -->
  <ProductSelectionDialog ref="productSelectionRef" @success="handleSmartPurchaseSubmit" />
</template>

<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { dateFormatter2 } from '@/utils/formatTime'
import download from '@/utils/download'
import {
  getPurchaseChannelSuggestions,
  PurchaseOrderApi,
  PurchaseOrderVO
} from '@/api/erp/purchase/order'
import PurchaseOrderForm from './PurchaseOrderForm.vue'
import ProductSelectionDialog from './components/ProductSelectionDialog.vue'
import { ProductApi, ProductVO } from '@/api/erp/product/product'
import { UserVO } from '@/api/system/user'
import * as UserApi from '@/api/system/user'
import { erpCountTableColumnFormatter, erpPriceTableColumnFormatter } from '@/utils'
import { SupplierApi, SupplierVO } from '@/api/erp/purchase/supplier'
import { ElLoading } from 'element-plus'

/** ERP 销售订单列表 */
defineOptions({ name: 'ErpPurchaseOrder' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(false) // 列表的加载中
const loadingText = ref('') // 加载时的文字提示
const list = ref<PurchaseOrderVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  no: undefined,
  supplierId: undefined,
  productId: undefined,
  orderTime: [],
  status: undefined,
  remark: undefined,
  creator: undefined,
  inStatus: undefined,
  returnStatus: undefined
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中
const smartPurchaseLoading = ref(false) // 智能采购的加载中
const productList = ref<ProductVO[]>([]) // 产品列表
const supplierList = ref<SupplierVO[]>([]) // 供应商列表
const userList = ref<UserVO[]>([]) // 用户列表
const productSelectionRef = ref() // 产品选择对话框引用

/** 查询列表 */
const getList = async () => {
  loading.value = true
  loadingText.value = '正在加载列表...'
  try {
    const data = await PurchaseOrderApi.getPurchaseOrderPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
    loadingText.value = ''
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 智能采购 */
const productSelectionDialogRef = ref()
const openProductSelectionDialog = () => {
  productSelectionDialogRef.value.open()
}

/** 处理生成采购订单 */
const handleGenerateOrders = async (selectedProducts: any[]) => {
  if (selectedProducts.length === 0) {
    return
  }
  try {
    loading.value = true
    // 1. 获取产品ID列表
    loadingText.value = '正在获取采购建议...'
    const productIds = selectedProducts.map((p) => p.id!)

    // 2. 调用后端接口获取采购建议
    const suggestions = await getPurchaseChannelSuggestions(productIds)
    if (!suggestions || suggestions.length === 0) {
      message.warning('未获取到有效的采购建议')
      loading.value = false
      return
    }

    // 3. 按渠道ID（供应商ID）对建议进行分组
    const groupedByChannel = suggestions.reduce((acc: Record<string, any[]>, suggestion) => {
      const product = selectedProducts.find((p) => p.id === suggestion.productId)
      if (product) {
        const item = { ...suggestion, product: product }
        ;(acc[suggestion.channelId] = acc[suggestion.channelId] || []).push(item)
      }
      return acc
    }, {})

    loadingText.value = '正在生成采购订单...'
    // 4. 为每个渠道（分组）创建一个采购订单
    const createPromises = Object.entries(groupedByChannel).map(
      ([channelId, suggestedItems]: [string, any[]]) => {
        const orderData: PurchaseOrderVO = {
          supplierId: Number(channelId), // 渠道ID即为供应商ID
          items: suggestedItems.map((item) => ({
            productId: item.product.id,
            count: item.product.purchaseCount, // 使用用户输入的数量
            productPrice: item.price // 使用建议的价格
          }))
          // 其他采购订单的默认值
        }
        return PurchaseOrderApi.createPurchaseOrder(orderData)
      }
    )

    // 5. 并发执行所有创建请求，并处理结果
    const results = await Promise.allSettled(createPromises)
    const successCount = results.filter((r) => r.status === 'fulfilled').length
    const failureCount = results.length - successCount

    // 6. 给出明确的反馈
    if (failureCount > 0) {
      const failedReasons = results.filter((r) => r.status === 'rejected').map((r: any) => r.reason.message || '未知错误').join('; ')
      message.alert(`智能采购完成！成功 ${successCount} 张，失败 ${failureCount} 张。失败原因：${failedReasons}`, '操作结果')
    } else {
      message.success(`智能采购完成！成功生成 ${successCount} 张采购订单。`)
    }
    await getList() // 刷新列表
  } catch (e) {
    console.error('智能采购失败', e)
    message.error('智能采购过程中发生未知错误，请查看控制台。')
  } finally {
    loading.value = false
    loadingText.value = ''
  }
}

/** 删除按钮操作 */
const handleDelete = async (ids: number[]) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await PurchaseOrderApi.deletePurchaseOrder(ids)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
    selectionList.value = selectionList.value.filter((item) => !ids.includes(item.id))
  } catch {}
}

/** 审批/反审批操作 */
const handleUpdateStatus = async (id: number, status: number) => {
  try {
    // 审批的二次确认
    await message.confirm(`确定${status === 20 ? '审批' : '反审批'}该订单吗？`)
    // 发起审批
    await PurchaseOrderApi.updatePurchaseOrderStatus(id, status)
    message.success(`${status === 20 ? '审批' : '反审批'}成功`)
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await PurchaseOrderApi.exportPurchaseOrder(queryParams)
    download.excel(data, '销售订单.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 选中操作 */
const selectionList = ref<PurchaseOrderVO[]>([])
const handleSelectionChange = (rows: PurchaseOrderVO[]) => {
  selectionList.value = rows
}

/** 智能采购操作 */
const openSmartPurchase = () => {
  productSelectionRef.value.open()
}

/** 处理智能采购提交 */
const handleSmartPurchaseSubmit = async (selectedProducts: any[]) => {
  if (!selectedProducts || selectedProducts.length === 0) {
    message.warning('请选择要采购的产品')
    return
  }

  let loadingInstance = null
  try {
    smartPurchaseLoading.value = true

    // 显示获取采购建议的loading
    loadingInstance = ElLoading.service({
      lock: true,
      text: '正在获取采购建议...'
    })

    // 调用智能采购API
    const result = await PurchaseOrderApi.createSmartPurchaseOrders({
      products: selectedProducts
    })

    // 更新loading文本
    if (loadingInstance) {
      loadingInstance.close()
      loadingInstance = ElLoading.service({
        lock: true,
        text: '正在生成采购订单...'
      })
    }

    // 处理结果
    if (result && result.success) {
      message.success(`智能采购完成！成功生成 ${result.orderCount || 0} 张采购订单`)
      await getList() // 刷新列表
    } else {
      message.error(result?.message || '智能采购失败，请重试')
    }
  } catch (error) {
    console.error('智能采购失败:', error)
    message.error('智能采购失败：' + (error.message || '未知错误'))
  } finally {
    smartPurchaseLoading.value = false
    if (loadingInstance) {
      loadingInstance.close()
    }
  }
}

/** 初始化 **/
onMounted(async () => {
  await getList()
  // 加载产品、仓库列表、供应商
  productList.value = await ProductApi.getProductSimpleList()
  supplierList.value = await SupplierApi.getSupplierSimpleList()
  userList.value = await UserApi.getSimpleUserList()
})
// TODO 芋艿：可优化功能：列表界面，支持导入
// TODO 芋艿：可优化功能：详情界面，支持打印
</script>

import request from '@/config/axios'

// 厂商 VO
export interface VehicleManufacturerVO {
  id: number // 厂商ID
  manufacturerCode: string // 厂商编码
  manufacturerName: string // 厂商名称
  manufacturerNameEn: string // 厂商英文名称
  status: number // 状态
}

// 厂商 API
export const VehicleManufacturerApi = {
  // 查询厂商分页
  getVehicleManufacturerPage: async (params: any) => {
    return await request.get({ url: `/erp/vehicle-manufacturer/page`, params })
  },

  // 查询厂商简单列表
  getVehicleManufacturerSimpleList: async () => {
    return await request.get({ url: `/erp/vehicle-manufacturer/simple-list` })
  },

  // 查询厂商详情
  getVehicleManufacturer: async (id: number) => {
    return await request.get({ url: `/erp/vehicle-manufacturer/get?id=` + id })
  },

  // 新增厂商
  createVehicleManufacturer: async (data: VehicleManufacturerVO) => {
    return await request.post({ url: `/erp/vehicle-manufacturer/create`, data })
  },

  // 修改厂商
  updateVehicleManufacturer: async (data: VehicleManufacturerVO) => {
    return await request.put({ url: `/erp/vehicle-manufacturer/update`, data })
  },

  // 删除厂商
  deleteVehicleManufacturer: async (id: number) => {
    return await request.delete({ url: `/erp/vehicle-manufacturer/delete?id=` + id })
  },

  // 导出厂商 Excel
  exportVehicleManufacturer: async (params) => {
    return await request.download({ url: `/erp/vehicle-manufacturer/export-excel`, params })
  },

// ==================== 子表（车型） ====================

  // 获得车型分页
  getVehicleModelPage: async (params) => {
    return await request.get({ url: `/erp/vehicle-manufacturer/vehicle-model/page`, params })
  },

  // 根据厂商ID获得车型简单列表
  getVehicleModelSimpleListByManufacturerId: async (manufacturerId: number) => {
    return await request.get({ url: `/erp/vehicle-manufacturer/vehicle-model/simple-list`, params: { manufacturerId } })
  },
  // 新增车型
  createVehicleModel: async (data) => {
    return await request.post({ url: `/erp/vehicle-manufacturer/vehicle-model/create`, data })
  },

  // 修改车型
  updateVehicleModel: async (data) => {
    return await request.put({ url: `/erp/vehicle-manufacturer/vehicle-model/update`, data })
  },

  // 删除车型
  deleteVehicleModel: async (id: number) => {
    return await request.delete({ url: `/erp/vehicle-manufacturer/vehicle-model/delete?id=` + id })
  },

  // 获得车型
  getVehicleModel: async (id: number) => {
    return await request.get({ url: `/erp/vehicle-manufacturer/vehicle-model/get?id=` + id })
  }
}

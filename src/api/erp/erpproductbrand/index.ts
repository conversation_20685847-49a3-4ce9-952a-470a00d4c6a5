import request from '@/config/axios'

// 产品品牌 VO
export interface ProductBrandVO {
  id: number // 主键ID
  name: string // 品牌中文名称
  nameEn: string // 品牌英文名称
  code: string // 品牌编码（唯一标识）
  description: string // 品牌描述
  logoUrl: string // 品牌Logo地址
  originCountry: string // 品牌原产国
  website: string // 品牌官网
  contactEmail: string // 品牌联系邮箱
  status: number // 状态（0-停用，1-正常，2-审核中，3-已拒绝）
  sortWeight: number // 排序权重（数值越大越靠前）
}

// 产品品牌 API
export const ProductBrandApi = {
  // 查询产品品牌分页
  getProductBrandPage: async (params: any) => {
    return await request.get({ url: `/erp/product-brand/page`, params })
  },

  // 查询产品品牌详情
  getProductBrand: async (id: number) => {
    return await request.get({ url: `/erp/product-brand/get?id=` + id })
  },

  // 新增产品品牌
  createProductBrand: async (data: ProductBrandVO) => {
    return await request.post({ url: `/erp/product-brand/create`, data })
  },

  // 修改产品品牌
  updateProductBrand: async (data: ProductBrandVO) => {
    return await request.put({ url: `/erp/product-brand/update`, data })
  },

  // 删除产品品牌
  deleteProductBrand: async (id: number) => {
    return await request.delete({ url: `/erp/product-brand/delete?id=` + id })
  },

  // 导出产品品牌 Excel
  exportProductBrand: async (params) => {
    return await request.download({ url: `/erp/product-brand/export-excel`, params })
  },

  // 查询产品品牌精简列表
  getProductBrandSimpleList: async () => {
    return await request.get({ url: `/erp/product-brand/simple-list` })
  }
}

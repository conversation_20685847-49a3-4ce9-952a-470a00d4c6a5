import request from '@/config/axios'

// 标签元素 VO
export interface LabelElementVO {
  id: string
  type: 'text' | 'barcode' | 'image' | 'line'
  x: number
  y: number
  width: number
  height: number
  content?: string
  fontSize?: number
  fontWeight?: string
  color?: string
  backgroundColor?: string
  textAlign?: 'left' | 'center' | 'right'
  barcodeOptions?: {
    format?: string
    width?: number
    height?: number
    displayValue?: boolean
    fontSize?: number
    textMargin?: number
    background?: string
    lineColor?: string
  }
  dataField?: string // 数据绑定字段
}

// 标签模板 VO
export interface LabelTemplateVO {
  id?: number
  name: string
  template: string
  templateConfig: string // JSON 字符串
  labelWidth: number
  labelHeight: number
  barcodeFormat: string
  templateType: number // 1产品标签,2入库标签,3出库标签
  productId?: number
  isDefault: boolean
  status: number
  remark?: string
  previewData?: string
  elements?: LabelElementVO[]
}

// 标签预览请求 VO
export interface LabelPreviewReqVO {
  templateId?: number
  templateConfig?: string
  productId?: number
  customData?: Record<string, any>
}

// 标签预览响应 VO
export interface LabelPreviewRespVO {
  html: string
  dataUrl: string
}

// 批量生成标签请求 VO
export interface BatchGenerateLabelsReqVO {
  templateId: number
  sourceType: 'STOCK_IN' | 'STOCK_OUT' | 'PRODUCT' | 'MANUAL'
  sourceId?: number
  products: ProductLabelInfo[]
}

// 产品标签信息
export interface ProductLabelInfo {
  productId: number
  productName: string
  productCode: string
  barCode: string
  quantity: number
  customData?: Record<string, any>
}

// 批量生成标签响应 VO
export interface BatchGenerateLabelsRespVO {
  taskId: number
  taskNo: string
  totalCount: number
  labels: LabelDataVO[]
}

// 标签数据 VO
export interface LabelDataVO {
  id: string
  productId: number
  productName: string
  productCode: string
  barCode: string
  labelHtml: string
  labelData: Record<string, any>
}

// 打印任务 VO
export interface PrintTaskVO {
  id: number
  taskNo: string
  templateId: number
  templateName?: string
  sourceType: string
  sourceId?: number
  totalCount: number
  printedCount: number
  status: number
  printTime?: Date
  createTime: Date
}

// 打印任务明细 VO
export interface PrintTaskDetailVO {
  id: number
  taskId: number
  productId: number
  productName: string
  productCode: string
  barcodeValue: string
  printCount: number
  labelData: Record<string, any>
  status: number
}

// 标签模板 API
export const LabelTemplateApi = {
  // 查询标签模板分页
  getLabelTemplatePage: async (params: any) => {
    return await request.get({ url: `/erp/barcode-template/page`, params })
  },

  // 查询标签模板列表
  getLabelTemplateList: async (params?: any) => {
    return await request.get({ url: `/erp/barcode-template/list`, params })
  },

  // 查询标签模板详情
  getLabelTemplate: async (id: number) => {
    return await request.get({ url: `/erp/barcode-template/get?id=` + id })
  },

  // 新增标签模板
  createLabelTemplate: async (data: LabelTemplateVO) => {
    return await request.post({ url: `/erp/barcode-template/create`, data })
  },

  // 修改标签模板
  updateLabelTemplate: async (data: LabelTemplateVO) => {
    return await request.put({ url: `/erp/barcode-template/update`, data })
  },

  // 删除标签模板
  deleteLabelTemplate: async (id: number) => {
    return await request.delete({ url: `/erp/barcode-template/delete?id=` + id })
  },

  // 复制标签模板
  copyLabelTemplate: async (id: number, name: string) => {
    return await request.post({ 
      url: `/erp/barcode-template/copy`, 
      data: { id, name } 
    })
  },

  // 设置默认模板
  setDefaultTemplate: async (id: number) => {
    return await request.put({ url: `/erp/barcode-template/set-default?id=` + id })
  },

  // 预览标签
  previewLabel: async (data: LabelPreviewReqVO) => {
    return await request.post({ url: `/erp/barcode-template/preview`, data })
  },

  // 获取产品的标签模板列表
  getProductLabelTemplates: async (productId: number) => {
    return await request.get({ url: `/erp/barcode-template/product/${productId}` })
  },

  // 获取指定类型的模板列表
  getTemplatesByType: async (templateType: number) => {
    return await request.get({ 
      url: `/erp/barcode-template/list`, 
      params: { templateType } 
    })
  }
}

// 标签打印 API
export const LabelPrintApi = {
  // 批量生成标签
  batchGenerateLabels: async (data: BatchGenerateLabelsReqVO) => {
    return await request.post({ url: `/erp/label-print/batch-generate`, data })
  },

  // 查询打印任务分页
  getPrintTaskPage: async (params: any) => {
    return await request.get({ url: `/erp/label-print/task/page`, params })
  },

  // 查询打印任务详情
  getPrintTask: async (id: number) => {
    return await request.get({ url: `/erp/label-print/task/get?id=` + id })
  },

  // 查询打印任务明细
  getPrintTaskDetails: async (taskId: number) => {
    return await request.get({ url: `/erp/label-print/task/${taskId}/details` })
  },

  // 执行打印
  executePrint: async (taskId: number) => {
    return await request.post({ url: `/erp/label-print/task/${taskId}/print` })
  },

  // 取消打印任务
  cancelPrintTask: async (taskId: number) => {
    return await request.put({ url: `/erp/label-print/task/${taskId}/cancel` })
  },

  // 删除打印任务
  deletePrintTask: async (id: number) => {
    return await request.delete({ url: `/erp/label-print/task/delete?id=` + id })
  },

  // 根据入库单生成标签
  generateLabelsFromStockIn: async (stockInId: number, templateId: number) => {
    return await request.post({ 
      url: `/erp/label-print/generate-from-stock-in`, 
      data: { stockInId, templateId } 
    })
  },

  // 根据产品生成标签
  generateLabelsFromProducts: async (data: {
    templateId: number
    products: { productId: number; quantity: number }[]
  }) => {
    return await request.post({ 
      url: `/erp/label-print/generate-from-products`, 
      data 
    })
  }
}

// 标签数据字段定义
export const LABEL_DATA_FIELDS = {
  // 产品相关字段
  productId: '产品ID',
  productName: '产品名称',
  productCode: '产品编码',
  barCode: '产品条码',
  universalProductCode: '通用产品码', // 添加通用产品码字段
  salePrice: '销售价格',
  purchasePrice: '采购价格',
  minPrice: '最低价格',
  standard: '产品规格',
  weight: '重量',
  productBrandName: '品牌名称',
  categoryName: '分类名称',
  unitName: '单位名称',
  
  // 入库相关字段
  stockInNo: '入库单号',
  inTime: '入库时间',
  warehouseName: '仓库名称',
  supplierName: '供应商名称',
  
  // 出库相关字段
  stockOutNo: '出库单号',
  outTime: '出库时间',
  customerName: '客户名称',
  
  // 系统字段
  currentDate: '当前日期',
  currentTime: '当前时间',
  printTime: '打印时间'
}

// 条码格式选项
export const BARCODE_FORMATS = [
  { label: 'CODE128', value: 'CODE128' },
  { label: 'EAN13', value: 'EAN13' },
  { label: 'EAN8', value: 'EAN8' },
  { label: 'CODE39', value: 'CODE39' },
  { label: 'ITF14', value: 'ITF14' },
  { label: 'MSI', value: 'MSI' },
  { label: 'Pharmacode', value: 'pharmacode' },
  { label: 'Codabar', value: 'codabar' }
]

// 注释：模板类型已简化，不再区分类型，统一用于入库场景
// 保留接口兼容性，默认使用入库标签类型(2)
